import React from 'react'
import { ExternalLink, Github, Flame, Bot, Leaf, Camera, Cloud, Car, Globe, Code, Accessibility } from 'lucide-react'

const Projects = () => {
  const projects = [
    {
      title: "Système d'Incendie",
      description: "Système de détection et d'alerte incendie utilisant des capteurs et microcontrôleurs",
      icon: <Flame size={24} />,
      technologies: ["Arduino", "C++", "Capteurs"],
      category: "Embarqué"
    },
    {
      title: "Robot Assistant Humanoïde",
      description: "Robot intelligent capable d'interactions et d'assistance aux utilisateurs",
      icon: <Bot size={24} />,
      technologies: ["Python", "IA", "Robotique"],
      category: "Robotique"
    },
    {
      title: "Smart Plant",
      description: "Système intelligent de surveillance et d'arrosage automatique des plantes",
      icon: <Leaf size={24} />,
      technologies: ["ESP32", "IoT", "Capteurs"],
      category: "IoT"
    },
    {
      title: "Caméra ESP32-CAM Reconnaissance Faciale",
      description: "Système de reconnaissance faciale en temps réel avec ESP32-CAM",
      icon: <Camera size={24} />,
      technologies: ["ESP32-CAM", "OpenCV", "Python"],
      category: "IA"
    },
    {
      title: "Application Météo Web",
      description: "Application web de prévisions météorologiques avec interface moderne",
      icon: <Cloud size={24} />,
      technologies: ["Python", "Flask", "API"],
      category: "Web"
    },
    {
      title: "Application Web Formule 1",
      description: "Plateforme web dédiée aux informations et statistiques de Formule 1",
      icon: <Car size={24} />,
      technologies: ["Web", "Base de données"],
      category: "Web"
    },
    {
      title: "Site pour Personnes Handicapées",
      description: "Site web accessible pour les personnes handicapées en Arabie Saoudite",
      icon: <Accessibility size={24} />,
      technologies: ["Web", "Accessibilité"],
      category: "Web"
    },
    {
      title: "Plateforme de Code Source",
      description: "Plateforme similaire à GitHub pour l'enregistrement et la gestion de codes source",
      icon: <Code size={24} />,
      technologies: ["Web", "Base de données"],
      category: "Plateforme"
    },
    {
      title: "Voiture Intelligente",
      description: "Projet de véhicule autonome avec capteurs et intelligence artificielle",
      icon: <Car size={24} />,
      technologies: ["IA", "Capteurs", "Embarqué"],
      category: "IA"
    }
  ]

  const categories = ["Tous", "Embarqué", "IoT", "IA", "Web", "Robotique", "Plateforme"]
  const [selectedCategory, setSelectedCategory] = React.useState("Tous")

  const filteredProjects = selectedCategory === "Tous"
    ? projects
    : projects.filter(project => project.category === selectedCategory)

  return (
    <section id="projects" className="projects">
      <div className="container">
        <h2 className="section-title">Mes Projets</h2>

        <div className="project-filters">
          {categories.map(category => (
            <button
              key={category}
              className={`filter-btn ${selectedCategory === category ? 'active' : ''}`}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </button>
          ))}
        </div>

        <div className="projects-grid">
          {filteredProjects.map((project, index) => (
            <div key={index} className="project-card">
              <div className="project-header">
                <div className="project-icon">{project.icon}</div>
                <span className="project-category">{project.category}</span>
              </div>

              <h3 className="project-title">{project.title}</h3>
              <p className="project-description">{project.description}</p>

              <div className="project-technologies">
                {project.technologies.map((tech, techIndex) => (
                  <span key={techIndex} className="tech-tag">
                    {tech}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Projects
