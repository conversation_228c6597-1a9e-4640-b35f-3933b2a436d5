import React from 'react'
import { ExternalLink, Github, Trash2, Bot, Smartphone, Thermometer, Droplets, Wind, QrCode, MessageCircle } from 'lucide-react'

const Projects = () => {
  const projects = [
    {
      title: "Poubelle Intelligente",
      description: "Système automatisé s'ouvrant sans contact via un capteur à ultrasons. Détection et gestion des gaz et incendies avec alerte sonore via buzzer intégré.",
      icon: <Trash2 size={24} />,
      technologies: ["Arduino", "C++", "Capteurs à ultrasons", "Capteurs de gaz", "Buzzer"],
      category: "Embarqué"
    },
    {
      title: "Application To-Do List",
      description: "Application mobile permettant de gérer des tâches avec fonctionnalités d'ajout, modification, suppression et rappels.",
      icon: <Smartphone size={24} />,
      technologies: ["Android Studio", "Java/Kotlin", "SQLite"],
      category: "Mobile"
    },
    {
      title: "Robot Assistant Humanoïde Autonome",
      description: "Robot capable de se déplacer de manière autonome avec interaction vocale via API Gemini. Intégration de capteurs pour mesurer température, humidité et détecter les gaz.",
      icon: <Bot size={24} />,
      technologies: ["Python", "API Gemini", "Capteurs", "Reconnaissance vocale"],
      category: "Robotique"
    },
    {
      title: "Système de Mesure Environnementale",
      description: "Intégration de capteurs pour la mesure de la température et de l'humidité dans le cadre du robot assistant.",
      icon: <Thermometer size={24} />,
      technologies: ["Capteurs de température", "Capteurs d'humidité", "Python"],
      category: "IoT"
    },
    {
      title: "Détection de Gaz Intelligente",
      description: "Système de détection de gaz intégré au robot assistant pour la surveillance de la qualité de l'air.",
      icon: <Wind size={24} />,
      technologies: ["Capteurs de gaz", "Python", "Alertes"],
      category: "IoT"
    },
    {
      title: "Reconnaissance d'Objets et QR Codes",
      description: "Système de reconnaissance d'objets et détection de QR codes pour le robot assistant humanoïde.",
      icon: <QrCode size={24} />,
      technologies: ["OpenCV", "Python", "Vision par ordinateur"],
      category: "IA"
    },
    {
      title: "API Météo en Temps Réel",
      description: "Intégration d'une API météo pour fournir des informations météorologiques en temps réel via le robot assistant.",
      icon: <Droplets size={24} />,
      technologies: ["API météo", "Python", "Données en temps réel"],
      category: "API"
    },
    {
      title: "Interaction Vocale Intelligente",
      description: "Système d'interaction vocale utilisant l'API Gemini ou apprentissage automatique pour le robot assistant.",
      icon: <MessageCircle size={24} />,
      technologies: ["API Gemini", "Python", "Traitement du langage naturel"],
      category: "IA"
    }
  ]

  const categories = ["Tous", "Embarqué", "Mobile", "Robotique", "IoT", "IA", "API"]
  const [selectedCategory, setSelectedCategory] = React.useState("Tous")

  const filteredProjects = selectedCategory === "Tous"
    ? projects
    : projects.filter(project => project.category === selectedCategory)

  return (
    <section id="projects" className="projects">
      <div className="container">
        <h2 className="section-title">Mes Projets</h2>

        <div className="project-filters">
          {categories.map(category => (
            <button
              key={category}
              className={`filter-btn ${selectedCategory === category ? 'active' : ''}`}
              onClick={() => setSelectedCategory(category)}
            >
              {category}
            </button>
          ))}
        </div>

        <div className="projects-grid">
          {filteredProjects.map((project, index) => (
            <div key={index} className="project-card">
              <div className="project-header">
                <div className="project-icon">{project.icon}</div>
                <span className="project-category">{project.category}</span>
              </div>

              <h3 className="project-title">{project.title}</h3>
              <p className="project-description">{project.description}</p>

              <div className="project-technologies">
                {project.technologies.map((tech, techIndex) => (
                  <span key={techIndex} className="tech-tag">
                    {tech}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Projects
