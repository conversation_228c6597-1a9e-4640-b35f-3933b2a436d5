import React from 'react'
import { Gith<PERSON>, Linkedin, Mail, Download } from 'lucide-react'

const Hero = () => {
  return (
    <section id="hero" className="hero">
      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              <PERSON><PERSON><PERSON>, je suis <span className="highlight">Dhia Soudani</span>
            </h1>
            <h2 className="hero-subtitle">
              Développeur Systèmes Embarqués & Robotique
            </h2>
            <p className="hero-description">
              Jeune diplômé d'une licence en Systèmes Embarqués de l'Institut Supérieur de l'Informatique (ISI),
              passionné par les systèmes embarqués, la robotique et la programmation. À la recherche d'une alternance
              pour approfondir mes compétences et contribuer à des projets innovants dans les domaines du développement
              embarqué et de la robotique.
            </p>
            
            <div className="hero-buttons">
             <a
  href="/cv-dhia.pdf"
  download="cv-dhia.pdf"
  className="btn btn-primary"
  title="Télécharger le CV de Dhia Soudani"
>
  <Download size={20} />
  Télécharger CV
</a>

              <button className="btn btn-secondary" onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}>
                Me contacter
              </button>
            </div>

            <div className="social-links">
              <a href="#" className="social-link" aria-label="GitHub">
                <Github size={24} />
              </a>
              <a href="#" className="social-link" aria-label="LinkedIn">
                <Linkedin size={24} />
              </a>
              <a href="#" className="social-link" aria-label="Email">
                <Mail size={24} />
              </a>
            </div>
          </div>

          <div className="hero-image">
            <div className="image-placeholder">
              <div className="profile-circle">
                <span>DS</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
