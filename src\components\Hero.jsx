import React from 'react'
import { G<PERSON><PERSON>, Linkedin, Mail, Download } from 'lucide-react'

const Hero = () => {
  return (
    <section id="hero" className="hero">
      <div className="container">
        <div className="hero-content">
          <div className="hero-text">
            <h1 className="hero-title">
              <PERSON><PERSON><PERSON>, je suis <span className="highlight">Aymen Mansouri</span>
            </h1>
            <h2 className="hero-subtitle">
              Développeur Systèmes Embarqués & IoT
            </h2>
            <p className="hero-description">
              Jeune diplômé en Licence Systèmes Embarqués et IoT, passionné par l'électronique embarquée, 
              la robotique et l'IA. Je recherche une alternance pour valoriser mes compétences en développement 
              embarqué et contribuer activement aux projets innovants.
            </p>
            
            <div className="hero-buttons">
             <a
  href="/cv-aymen.pdf"
  download="cv-aymen.pdf"
  className="btn btn-primary"
  title="Télécharger le CV d'Aymen Mansouri"
>
  <Download size={20} />
  Télécharger CV
</a>

              <button className="btn btn-secondary" onClick={() => document.getElementById('contact').scrollIntoView({ behavior: 'smooth' })}>
                Me contacter
              </button>
            </div>

            <div className="social-links">
              <a href="#" className="social-link" aria-label="GitHub">
                <Github size={24} />
              </a>
              <a href="#" className="social-link" aria-label="LinkedIn">
                <Linkedin size={24} />
              </a>
              <a href="#" className="social-link" aria-label="Email">
                <Mail size={24} />
              </a>
            </div>
          </div>

          <div className="hero-image">
            <div className="image-placeholder">
              <div className="profile-circle">
                <span>AM</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default Hero
