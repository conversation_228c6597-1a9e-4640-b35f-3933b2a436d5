import React from 'react'
import { GraduationCap, Target, Heart } from 'lucide-react'

const About = () => {
  return (
    <section id="about" className="about">
      <div className="container">
        <h2 className="section-title">À propos de moi</h2>
        
        <div className="about-content">
          <div className="about-text">
            <p className="about-intro">
              Diplômé de l'Institut Supérieur de l'Informatique (ISI) en Systèmes Embarqués,
              je suis un développeur passionné par l'innovation technologique et les défis techniques.
              Maîtrise des langages C, C++ et C embarqué, avec une certification Cisco : C Essentials I.
            </p>
            
            <div className="about-highlights">
              <div className="highlight-item">
                <GraduationCap className="highlight-icon" size={24} />
                <div>
                  <h3>Formation</h3>
                  <p>Licence Systèmes Embarqués</p>
                  <p>Institut Supérieur de l'Informatique (ISI)</p>
                </div>
              </div>

              <div className="highlight-item">
                <Target className="highlight-icon" size={24} />
                <div>
                  <h3>Objectif</h3>
                  <p>Recherche d'une alternance en développement embarqué</p>
                  <p>Contribution active aux projets innovants</p>
                </div>
              </div>

              <div className="highlight-item">
                <Heart className="highlight-icon" size={24} />
                <div>
                  <h3>Passions</h3>
                  <p>Systèmes embarqués, Robotique, Programmation</p>
                  <p>Microcontrôleurs et développement embarqué</p>
                </div>
              </div>
            </div>
          </div>

          <div className="about-stats">
            <div className="stat-item">
              <span className="stat-number">3+</span>
              <span className="stat-label">Projets académiques</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">8+</span>
              <span className="stat-label">Technologies maîtrisées</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">4+</span>
              <span className="stat-label">Langages de programmation</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default About
