import React, { useState } from 'react'
import { Mail, Phone, MapPin, Send, Linkedin } from 'lucide-react'

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  })

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    console.log('Form submitted:', formData)
    alert('Message envoyé ! Je vous répondrai bientôt.')
    setFormData({ name: '', email: '', subject: '', message: '' })
  }

  return (
    <section id="contact" className="contact">
      <div className="container">
        <h2 className="section-title">Contactez-moi</h2>
        <p className="section-subtitle">
          Intéressé par une collaboration ? N'hésitez pas à me contacter !
        </p>

        <div className="contact-content">
          {/* INFO DE CONTACT */}
          <div className="contact-info">
            <h3>Informations de contact</h3>

            <div className="contact-item">
              <Mail className="contact-icon" size={20} />
              <div>
                <h4>Email</h4>
                <p><EMAIL></p>
              </div>
            </div>

            <div className="contact-item">
              <Phone className="contact-icon" size={20} />
              <div>
                <h4>Téléphone</h4>
                <p>+216 90 322 383</p>
              </div>
            </div>

            <div className="contact-item">
              <MapPin className="contact-icon" size={20} />
              <div>
                <h4>Localisation</h4>
                <p>Tunisie</p>
              </div>
            </div>

            <div className="contact-social">
              <h4>Réseaux sociaux</h4>
              <div className="social-links">
                <a
                  href="https://www.linkedin.com/in/dhia-soudani"
                  className="social-link"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Linkedin size={24} />
                  <span>linkedin.com/in/dhia-soudani</span>
                </a>
              </div>
            </div>
          </div>

          {/* FORMULAIRE */}
          <form className="contact-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label htmlFor="name">Nom complet</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="email">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="subject">Sujet</label>
              <input
                type="text"
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="message">Message</label>
              <textarea
                id="message"
                name="message"
                rows="5"
                value={formData.message}
                onChange={handleChange}
                required
              ></textarea>
            </div>

            <button type="submit" className="btn btn-primary">
              <Send size={20} />
              Envoyer le message
            </button>
          </form>
        </div>
      </div>
    </section>
  )
}

export default Contact
