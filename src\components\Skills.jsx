import React from 'react'
import { Code, Database, Monitor, Cpu, Smartphone, Globe } from 'lucide-react'

const Skills = () => {
  const skillCategories = [
    {
      title: "Langages de Programmation",
      icon: <Code size={24} />,
      skills: ["C (certifié Cisco : C Essentials I)", "C++", "C embarqué", "Python"],
      color: "blue"
    },
    {
      title: "Outils et Plateformes",
      icon: <Monitor size={24} />,
      skills: ["STM32CubeIDE", "FreeRTOS", "Qt", "Raspberry Pi OS", "ThingSpeak", "Android Studio", "Thomy", "VNC viewer", "VS Code"],
      color: "green"
    },
    {
      title: "Bibliothèques",
      icon: <Database size={24} />,
      skills: ["STM32 HAL library", "OpenCV", "SQLite", "Picamera2"],
      color: "purple"
    },
    {
      title: "Cartes Électroniques",
      icon: <Cpu size={24} />,
      skills: ["Raspberry Pi", "Arduino UNO", "Arduino Mega 2560", "ESP32 (module WROOM-32)", "Cartes STM32"],
      color: "orange"
    },
    {
      title: "Capteurs et Composants",
      icon: <Monitor size={24} />,
      skills: ["Capteurs à ultrasons", "Capteurs de gaz", "Capteurs d'incendie", "Buzzer", "Capteurs de température", "Capteurs d'humidité"],
      color: "red"
    },
    {
      title: "Développement et Intégration",
      icon: <Globe size={24} />,
      skills: ["Développement embarqué", "Robotique", "API Gemini", "API météo", "Reconnaissance d'objets", "Détection QR codes"],
      color: "teal"
    }
  ]

  return (
    <section id="skills" className="skills">
      <div className="container">
        <h2 className="section-title">Compétences Techniques</h2>
        
        <div className="skills-grid">
          {skillCategories.map((category, index) => (
            <div key={index} className={`skill-category ${category.color}`}>
              <div className="skill-header">
                <div className="skill-icon">
                  {category.icon}
                </div>
                <h3 className="skill-title">{category.title}</h3>
              </div>
              
              <div className="skill-list">
                {category.skills.map((skill, skillIndex) => (
                  <span key={skillIndex} className="skill-tag">
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Skills
