import React from 'react'
import { Code, Database, Monitor, Cpu, Smartphone, Globe } from 'lucide-react'

const Skills = () => {
  const skillCategories = [
    {
      title: "Langages de Programmation",
      icon: <Code size={24} />,
      skills: ["Pascal", "C", "C++", "Python"],
      color: "blue"
    },
    {
      title: "Bases de Données",
      icon: <Database size={24} />,
      skills: ["MySQL", "Firebase", "SQLite"],
      color: "green"
    },
    {
      title: "Systèmes d'Exploitation",
      icon: <Monitor size={24} />,
      skills: ["Linux (Script Shell)", "CMD"],
      color: "purple"
    },
    {
      title: "Matériel Embarqué",
      icon: <Cpu size={24} />,
      skills: ["Arduino", "ESP32", "ESP32-CAM", "ESP8266", "STM32", "Raspberry Pi"],
      color: "orange"
    },
    {
      title: "Environnements de Développement",
      icon: <Monitor size={24} />,
      skills: ["Android Studio", "Visual Studio", "Code Blocks", "Arduino IDE", "MATLAB", "Imager", "VNC"],
      color: "red"
    },
    {
      title: "Développement",
      icon: <Globe size={24} />,
      skills: ["Programmation Mobile", "Développement Web", "OpenCV", "Flask", "Firebase"],
      color: "teal"
    }
  ]

  return (
    <section id="skills" className="skills">
      <div className="container">
        <h2 className="section-title">Compétences Techniques</h2>
        
        <div className="skills-grid">
          {skillCategories.map((category, index) => (
            <div key={index} className={`skill-category ${category.color}`}>
              <div className="skill-header">
                <div className="skill-icon">
                  {category.icon}
                </div>
                <h3 className="skill-title">{category.title}</h3>
              </div>
              
              <div className="skill-list">
                {category.skills.map((skill, skillIndex) => (
                  <span key={skillIndex} className="skill-tag">
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  )
}

export default Skills
