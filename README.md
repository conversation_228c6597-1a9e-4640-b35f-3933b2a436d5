# Portfolio Aymen Mansouri

Portfolio professionnel moderne développé avec React et Vite, présentant les compétences et projets d'Aymen Mansouri, développeur en Systèmes Embarqués et IoT.

## 🚀 Aperçu

Ce portfolio présente :
- **Profil professionnel** : Jeune diplômé en Licence Systèmes Embarqués et IoT
- **Compétences techniques** : Langages de programmation, bases de données, matériel embarqué
- **Projets réalisés** : 9+ projets innovants en IoT, IA, robotique et développement web
- **Interface moderne** : Design responsive et animations fluides

## 🛠️ Technologies utilisées

- **Frontend** : React 19, Vite
- **Styling** : CSS3 moderne avec variables CSS, Flexbox, Grid
- **Icônes** : Lucide React
- **Fonts** : Google Fonts (Inter)
- **Responsive** : Mobile-first design

## 📋 Fonctionnalités

### ✨ Sections principales
- **Header** : Navigation fixe avec menu mobile
- **Hero** : Présentation principale avec appel à l'action
- **À propos** : Profil détaillé et statistiques
- **Compétences** : Technologies organisées par catégories
- **Projets** : Showcase filtrable des réalisations
- **Contact** : Formulaire de contact et informations

### 🎨 Design
- Interface moderne et professionnelle
- Animations CSS fluides
- Design entièrement responsive
- Palette de couleurs cohérente
- Typographie optimisée

## 🚀 Installation et utilisation

### Prérequis
- Node.js (version 16 ou supérieure)
- npm ou yarn

### Installation
```bash
# Cloner le repository
git clone [url-du-repo]
cd portflio

# Installer les dépendances
npm install

# Lancer le serveur de développement
npm run dev
```

### Scripts disponibles
```bash
# Développement
npm run dev

# Build de production
npm run build

# Prévisualisation du build
npm run preview

# Linting
npm run lint
```

## 📁 Structure du projet

```
src/
├── components/
│   ├── Header.jsx      # Navigation principale
│   ├── Hero.jsx        # Section d'accueil
│   ├── About.jsx       # Section à propos
│   ├── Skills.jsx      # Compétences techniques
│   ├── Projects.jsx    # Portfolio de projets
│   └── Contact.jsx     # Formulaire de contact
├── assets/             # Images et ressources
├── App.jsx            # Composant principal
├── App.css            # Styles principaux
├── index.css          # Styles globaux
└── main.jsx           # Point d'entrée
```

## 🎯 Compétences mises en avant

### Langages de programmation
- Pascal, C, C++, Python

### Bases de données
- MySQL, Firebase, SQLite

### Matériel embarqué
- Arduino, ESP32, ESP32-CAM, ESP8266, STM32, Raspberry Pi

### Environnements de développement
- Android Studio, Visual Studio, Code Blocks, Arduino IDE, MATLAB

### Technologies web
- Développement mobile et web, OpenCV, Flask, Firebase

## 📱 Projets présentés

1. **Système d'Incendie** - Détection et alerte incendie
2. **Robot Assistant Humanoïde** - Robot intelligent d'assistance
3. **Smart Plant** - Système d'arrosage automatique
4. **Reconnaissance Faciale ESP32-CAM** - IA en temps réel
5. **Application Météo Web** - Prévisions météorologiques
6. **Application Formule 1** - Statistiques F1
7. **Site Accessibilité** - Pour personnes handicapées
8. **Plateforme Code Source** - Gestion de projets
9. **Voiture Intelligente** - Véhicule autonome

## 🔧 Personnalisation

Pour adapter ce portfolio :

1. **Informations personnelles** : Modifier les données dans chaque composant
2. **Projets** : Mettre à jour la liste dans `Projects.jsx`
3. **Compétences** : Ajuster les catégories dans `Skills.jsx`
4. **Couleurs** : Modifier les variables CSS dans `App.css`
5. **Contenu** : Adapter les textes selon vos besoins

## 📞 Contact

- **Email** : <EMAIL>
- **Localisation** : Tunisie
- **Recherche** : Alternance en développement embarqué

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

---

Développé avec ❤️ par Aymen Mansouri
